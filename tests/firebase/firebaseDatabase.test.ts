import { describe, it, expect, beforeAll } from 'vitest'
import { getTestDb } from './utils'
import type { FirebaseDatabase } from '@firebase/database-types'
import { Auth } from 'firebase-admin/auth'
import type { FieldPath } from 'firebase-admin/firestore'
import type { Database as AdminDatabase } from 'firebase-admin/database'

import { useFirebaseDatabase } from '~/server/utils/firebaseDatabase'

describe('Firebase Database - utility functions', () => {
  let db: FirebaseDatabase
  let mockAdminProvider = () => ({
    db: db as unknown as AdminDatabase,
    auth: {} as Auth,
    fs: {} as FirebaseFirestore.Firestore,
    fsFieldPath: {} as typeof FieldPath,
  })
  beforeAll(async () => {
    db = await getTestDb()
  })
  it(`can push data and fetch from Firebase`, async () => {
    const { firebasePush, firebaseFetch } = useFirebaseDatabase('test-token', mockAdminProvider)
    const itemData = { name: 'Test Item', description: 'This is a test item.' }
    const newItemKey = await firebasePush(`/items`, itemData)
    expect(newItemKey).toBeDefined()
    const itemSnapshot = await firebaseFetch(`/items/${newItemKey}`)
    expect(itemSnapshot).toEqual(itemData)
  })
})
