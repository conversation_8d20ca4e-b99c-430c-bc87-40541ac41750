import { initializeTestEnvironment, type RulesTestEnvironment } from '@firebase/rules-unit-testing'
import { readFileSync } from 'fs'
import { join } from 'path'

let testEnv: RulesTestEnvironment

export async function setupFirebaseTestEnvironment() {
  if (!testEnv) {
    testEnv = await initializeTestEnvironment({
      projectId: 'web-workbench-test',
      database: {
        host: 'localhost',
        port: 9000,
        rules: readFileSync(join(__dirname, 'realtime.rules'), 'utf8'),
      },
    })
  }
  return testEnv
}

export async function getTestDb(userId = 'asKGRhh9iEbM06XXqxuhuuxcaKE2') {
  const env = await setupFirebaseTestEnvironment()
  return env.authenticatedContext(userId).database()
}
