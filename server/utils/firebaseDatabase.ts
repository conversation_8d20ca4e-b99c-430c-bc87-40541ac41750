import { FirebaseDatabase } from '@firebase/database-types'
import { Auth } from 'firebase-admin/auth'

// Add a helper function to validate token
export const useAuthenticatedFirebase = (event: any) => {
  const token = getCookie(event, 'session')
  if (!token) {
    throw new Error('Unauthorized: No session token found')
  }

  return useFirebaseDatabase(token)
}

export const useFirebaseDatabase = (token: string, adminProvider = useFirebaseAdmin) => {
  let fdb: FirebaseDatabase
  let fauth: Auth

  const { db, auth } = adminProvider()
  fdb = db
  fauth = auth
  /**
   * Firebase fetch realtime database
   * @param path realtime database ref e.g. "users", "users/key1"
   * @returns data
   */
  const firebaseFetch = async (path: string) => {
    // let decodedToken
    // try {
    //   decodedToken = await fauth.verifyIdToken(token)
    // } catch (error) {
    //   throw new Error(`Token verification failed: ${error}`)
    // }
    const ref = fdb.ref(path)
    const snapshot = await ref.once('value')
    if (snapshot.exists()) {
      return snapshot.val()
    } else {
      console.log('No data found at path:', path)
      throw new Error(`Data not found at path: ${path}`)
    }
  }

  /**
   * Firebase fetch realtime database with limit
   * @param path realtime database ref e.g. "users", "users/key1"
   * @param limit limit number of data to fetch
   * @returns data
   */
  const firebaseFetchLimit = async (path: string, limit: number) => {
    const ref = fdb.ref(path)
    const snapshot = await ref.limitToLast(limit).once('value')
    if (snapshot.exists()) {
      return snapshot.val()
    } else {
      throw new Error(`No data found at path: ${path}`)
    }
  }

  /**
   * Firebase fetch bulk data
   * @param path ref to fetch key from e.g. "users", "creditPackages"
   * @param keys array of keys
   * @returns requested data
   */
  const firebaseFetchBulk = async (path: string, keys: string[]) => {
    const keysData = await Promise.all(
      keys.map(async (key) => {
        const ref = fdb.ref(`${path}/${key}`)
        const snapshot = await ref.once('value')
        if (snapshot.exists()) {
          return {
            ...snapshot.val(),
            key,
          }
        } else {
          throw new Error(`No data found for key: ${key}`)
        }
      }),
    )
    return keysData
  }

  /**
   * Firebase fetch data on specific date
   * @param path ref to fetch data from e.g. "bookings"
   * @param startDate date iso format e.g. ""2020-01-03T00:00:00+08:00""
   * @param endDate date iso format e.g. ""2020-01-03T23:59:59+08:00""
   * @param child key to filter date name e.g. "date", "startDate"
   * @returns data
   */
  const firebaseFetchOnDateRange = async (
    path: string,
    startDate: string,
    endDate: string,
    child = 'date',
  ) => {
    const ref = fdb.ref(path)
    const snapshot = await ref.orderByChild(child).startAt(startDate).endAt(endDate).once('value')
    if (snapshot.exists()) {
      return snapshot.val()
    } else {
      throw new Error('No bookings found')
    }
  }

  const firebaseFetchOnChildValue = async (path: string, childKey: string, value: string) => {
    const ref = db.ref(path)
    const snapshot = await ref.orderByChild(childKey).equalTo(value).once('value')
    if (snapshot.exists()) {
      return snapshot.val()
    } else {
      throw new Error('No data found')
    }
  }

  const firebaseSet = async (path: string, data: any) => {
    const ref = fdb.ref(path)
    try {
      await ref.set(data)
      const snapshot = await ref.once('value')
      if (snapshot.exists()) {
        return snapshot.val()
      } else {
        throw new Error(`Cannot find path: ${path}`)
      }
    } catch (error) {
      throw new Error(`Cannot find path: ${path} with error: ${error}`)
    }
  }

  const firebasePush = async (path: string, data: any) => {
    const ref = fdb.ref(path)
    try {
      const newKey = await ref.push(data).key
      const snapshot = await fdb.ref(`${path}/${newKey}`).once('value')
      if (snapshot.exists()) {
        return newKey
      } else {
        throw new Error(`Could not find new workflow key: ${newKey}`)
      }
    } catch (error) {
      throw new Error(`Cannot find path: ${path} with error: ${error}`)
    }
  }

  const firebaseUpdate = async (path: string, data: any) => {
    const ref = fdb.ref(path)
    try {
      await ref.update(data)
    } catch (error) {
      throw new Error(`Cannot find path: ${path} with error: ${error}`)
    }
  }

  const firebaseRemove = async (path: string) => {
    const ref = fdb.ref(path)
    try {
      await ref.remove()
    } catch (error) {
      throw new Error(`Cannot find path: ${path} with error: ${error}`)
    }
  }

  const firebaseFetchEndBefore = async (path: string, endBefore: any, child = 'date') => {
    const ref = fdb.ref(path)
    const snapshot = await ref.orderByChild(child).endBefore(endBefore).once('value')
    if (snapshot.exists()) {
      return snapshot.val()
    } else {
      throw new Error('No bookings found')
    }
  }

  return {
    firebaseFetch,
    firebaseFetchLimit,
    firebaseFetchBulk,
    firebaseFetchOnDateRange,
    firebaseFetchOnChildValue,
    firebaseFetchEndBefore,
    firebaseSet,
    firebaseUpdate,
    firebasePush,
    firebaseRemove,
  }
}
